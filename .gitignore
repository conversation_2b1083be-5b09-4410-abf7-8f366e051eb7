# Multi-Tool Agent - Exclude artifacts and data files

# === PYTHON ===
# Virtual environments
.venv/
venv/
env/
ENV/
.env/
.virtualenv/

# Python cache and compiled files
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.egg-info/
dist/
build/
*.egg
.eggs/

# Python tools
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/
.coverage
htmlcov/
.tox/
.mypy_cache/
.dmypy.json
dmypy.json

# === NODE.JS / FRONTEND ===
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
.next/
out/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Vite
.vite/

# === ENVIRONMENT & SECRETS ===
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging
*.key
*.pem
.env.*
!.env.example

# === DEVELOPMENT TOOLS ===
# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
*~

# History files from Cursor editor
.history/

# === DATA & OUTPUT FILES ===
# Data and output directories
multi_tool_agent/data/
multi_tool_agent/crawl4ai_output/
multi_tool_agent/debug_output/
test_output/

# === MODEL CACHE ===
# Local embedding model cache (can be large ~2GB)
models/
*.model
*.bin
*.safetensors

# JSON data files (allow specific ones)
*.json
!package.json
!package-lock.json
!tsconfig*.json
!requirements*.txt
!multi_tool_agent/pedalheads_detailed_sample_20250529_150350.json

# JSONL files (large data files)
*.jsonl
!community_activities.jsonl

# === LOGS ===
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# === DATABASES ===
*.db
*.sqlite
*.sqlite3

# === OS SPECIFIC ===
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes

# Windows
ehthumbs.db
Thumbs.db
Thumbs.db:encryptable
desktop.ini

# Linux
*~

# === DOCKER ===
docker-compose.override.yml

# === EXTERNAL DEPENDENCIES ===
# Large external repositories (like graphiti)
graphiti/

# === TEMPORARY FILES ===
*.tmp
*.temp
temp/
tmp/
*.bak
*.backup

# === SPECIFIC TOOLS ===
# Neo4j
.neo4j/

# CocoIndex
.cocoindex/

# === BACKUP & COPY FILES ===
*~
*.orig
*.rej
*_backup
*_old
*_copy
*Copy*
*- Copy*

# === MISC ===
.lock-wscript
nul 

# === ARCHIVE ===
archive/ 