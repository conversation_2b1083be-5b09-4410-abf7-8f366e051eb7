"""
Rate-Limited LLM Wrapper for ADK Framework
Integrates rate limiting with Google ADK's LiteLlm model.
"""

import logging
import asyncio
import time
from typing import Any, Dict, Optional, AsyncIterator
from google.adk.models.lite_llm import LiteLlm
# Note: Using Any for request/response types to avoid import issues

from ..utils.rate_limiter import get_hybrid_manager, execute_llm_with_rate_limiting
from ..utils.api_monitor import record_api_call
from ..utils.request_queue import get_global_queue

logger = logging.getLogger(__name__)

class RateLimitedLiteLlm(LiteLlm):
    """
    Rate-limited wrapper around LiteLlm that respects Kimi K2's 10 RPM limit
    and uses hybrid model strategy.
    """
    
    def __init__(self, 
                 model: str, 
                 task_type: str = "unknown",
                 is_critical: bool = False,
                 **kwargs):
        """
        Initialize rate-limited LLM.
        
        Args:
            model: The model name (may be overridden by rate limiter)
            task_type: Type of task for model selection
            is_critical: Whether this is a critical reasoning task
            **kwargs: Additional arguments for LiteLlm
        """
        super().__init__(model=model, **kwargs)
        self.original_model = model
        self.task_type = task_type
        self.is_critical = is_critical
        self.hybrid_manager = get_hybrid_manager()
        
    async def generate_content_async(self, request, ctx) -> AsyncIterator:
        """
        Generate content with rate limiting and hybrid model selection.
        """
        start_time = time.time()
        selected_model = self.original_model
        success = True
        error_type = None

        try:
            # Use global request queue to manage concurrency
            queue = get_global_queue()
            request_id = f"{self.task_type}_{int(time.time() * 1000)}"

            async with queue.acquire_slot(request_id, self.task_type, self.is_critical):
                # Determine the best model for this task
                selected_model = self.hybrid_manager.get_model_for_task(self.task_type, self.is_critical)

                # Check if we should use Kimi with rate limiting
                should_use_kimi = await self.hybrid_manager.should_use_kimi(self.task_type, self.is_critical)

                if should_use_kimi and selected_model == self.hybrid_manager.kimi_model:
                    # Use Kimi with rate limiting
                    try:
                        async with self.hybrid_manager.kimi_limiter.acquire():
                            logger.info(f"🧠 Using Kimi K2 for {self.task_type} (critical={self.is_critical})")
                            selected_model = self.hybrid_manager.kimi_model

                            # Temporarily set model to Kimi
                            original_model = self.model
                            self.model = self.hybrid_manager.kimi_model

                            try:
                                async for response in super().generate_content_async(request, ctx):
                                    yield response
                                return  # Success with Kimi
                            finally:
                                # Restore original model
                                self.model = original_model

                    except Exception as e:
                        if "rate" in str(e).lower() or "429" in str(e):
                            logger.warning(f"🚫 Kimi rate limited for {self.task_type} - using fallback")
                            await self.hybrid_manager.kimi_limiter.record_rate_limit_error()
                            error_type = "rate_limit"
                            # Fall through to fallback
                        else:
                            success = False
                            error_type = type(e).__name__
                            raise

                # Use fallback model
                fallback_model = self.hybrid_manager.get_model_for_task(self.task_type, self.is_critical)
                if fallback_model != self.hybrid_manager.kimi_model:
                    logger.info(f"⚡ Using fallback model {fallback_model} for {self.task_type}")
                    selected_model = fallback_model

                    # Temporarily set model to fallback
                    original_model = self.model
                    self.model = fallback_model

                    try:
                        async for response in super().generate_content_async(request, ctx):
                            yield response
                    finally:
                        # Restore original model
                        self.model = original_model
                else:
                    # This shouldn't happen, but fallback to original behavior
                    logger.warning(f"⚠️ Unexpected fallback to Kimi for {self.task_type}")
                    async for response in super().generate_content_async(request, ctx):
                        yield response

        except Exception as e:
            success = False
            if error_type is None:
                error_type = type(e).__name__
            raise
        finally:
            # Record the API call for monitoring
            response_time = time.time() - start_time
            record_api_call(
                model=selected_model,
                task_type=self.task_type,
                is_critical=self.is_critical,
                success=success,
                error_type=error_type,
                response_time=response_time
            )

def create_rate_limited_model(model: str, 
                             task_type: str = "unknown", 
                             is_critical: bool = False,
                             **kwargs) -> RateLimitedLiteLlm:
    """
    Factory function to create rate-limited LLM models.
    
    Args:
        model: Base model name
        task_type: Type of task (reasoning, tool_execution, search, etc.)
        is_critical: Whether this is a critical reasoning task
        **kwargs: Additional LiteLlm arguments
        
    Returns:
        RateLimitedLiteLlm instance
    """
    return RateLimitedLiteLlm(
        model=model,
        task_type=task_type,
        is_critical=is_critical,
        **kwargs
    )

# Convenience functions for common model types
def create_reasoning_model(model: str = None, **kwargs) -> RateLimitedLiteLlm:
    """Create a model optimized for reasoning tasks (uses Kimi when available)."""
    from ..config import AgentConfig
    if model is None:
        model = AgentConfig.ORCHESTRATOR_MODEL
    return create_rate_limited_model(model, task_type="reasoning", is_critical=True, **kwargs)

def create_search_model(model: str = None, **kwargs) -> RateLimitedLiteLlm:
    """Create a model optimized for search tasks (uses fallback models)."""
    from ..config import AgentConfig
    if model is None:
        model = AgentConfig.SEARCH_AGENT_MODEL
    return create_rate_limited_model(model, task_type="search", is_critical=False, **kwargs)

def create_tool_model(model: str = None, **kwargs) -> RateLimitedLiteLlm:
    """Create a model optimized for tool execution (uses cheap models)."""
    from ..config import AgentConfig
    if model is None:
        model = getattr(AgentConfig, 'TOOL_AGENT_MODEL', AgentConfig.CHEAP_MODEL)
    return create_rate_limited_model(model, task_type="tool_execution", is_critical=False, **kwargs)

def create_response_model(model: str = None, **kwargs) -> RateLimitedLiteLlm:
    """Create a model optimized for response formatting (uses fallback models)."""
    from ..config import AgentConfig
    if model is None:
        model = AgentConfig.RESPONSE_AGENT_MODEL
    return create_rate_limited_model(model, task_type="formatting", is_critical=False, **kwargs)
