// Prefer explicit env var; otherwise choose default based on where the front-end is served.
const inferredDefault = typeof window !== 'undefined' && window.location.hostname.includes('localhost')
  ? 'http://localhost:8080'                  // Custom FastAPI server with WebSocket support
  : 'https://bc-activity-agent-5l7edhs3gq-uc.a.run.app'; // Cloud Run backend

export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || inferredDefault; 