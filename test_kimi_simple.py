#!/usr/bin/env python3
"""
Simple test script for Kimi K2 model integration.
"""

import asyncio
import logging
import sys
import os

# Add the multi_tool_agent directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'multi_tool_agent'))

from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types
from config import AgentConfig

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Simple test agent using Kimi K2
kimi_test_agent = Agent(
    name="KimiTestAgent",
    model=LiteLlm(model=AgentConfig.KIMI_MODEL),
    description="Test agent using the Kimi K2 model.",
    instruction="You are a helpful assistant powered by Kimi K2. Provide clear, detailed responses.",
)

async def test_kimi():
    """Test basic Kimi K2 functionality."""
    logger.info("🧪 Testing Kimi K2 integration...")
    
    # Check API key
    if not AgentConfig.NOVITA_API_KEY:
        logger.error("❌ NOVITA_API_KEY not found in environment")
        return False
    
    try:
        # Session setup
        session_service = InMemorySessionService()
        APP_NAME, USER_ID, SESSION_ID = "kimi_test", "user1", "session1"
        await session_service.create_session(
            app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
        )
        
        # Runner
        runner = Runner(agent=kimi_test_agent, app_name=APP_NAME, session_service=session_service)
        logger.info(f"✅ Runner created for {runner.agent.name}")
        
        # Test query
        query = "What are the benefits of swimming for children? Please provide 3 key benefits."
        logger.info(f"📝 Query: {query}")
        
        content = types.Content(role='user', parts=[types.Part(text=query)])
        
        response_text = "No response received."
        
        async for event in runner.run_async(
            user_id=USER_ID,
            session_id=SESSION_ID,
            new_message=content
        ):
            if event.is_final_response() and event.content and event.content.parts:
                response_text = event.content.parts[0].text.strip()
                break
        
        logger.info(f"🤖 Kimi Response:\n{response_text}")
        
        # Basic validation
        if len(response_text) > 50 and "swimming" in response_text.lower():
            logger.info("✅ Kimi K2 test PASSED!")
            return True
        else:
            logger.error("❌ Kimi K2 test FAILED - Invalid response")
            return False
            
    except Exception as e:
        logger.error(f"❌ Kimi K2 test FAILED: {str(e)}")
        return False

if __name__ == "__main__":
    # Enable debugging
    import litellm
    litellm.set_verbose = True
    
    success = asyncio.run(test_kimi())
    print(f"\n{'🎉 SUCCESS' if success else '❌ FAILED'}")
