"""
Simple response caching system to reduce API calls.
Caches LLM responses for similar queries to minimize API usage.
"""

import hashlib
import time
import json
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class ResponseCache:
    """Simple in-memory cache for LLM responses."""
    
    def __init__(self, ttl_minutes: int = 30):
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.ttl_seconds = ttl_minutes * 60
        self.hits = 0
        self.misses = 0
    
    def _generate_cache_key(self, query: str, filters: Dict[str, Any] = None) -> str:
        """Generate a cache key from query and filters."""
        # Normalize the query
        normalized_query = query.lower().strip()
        
        # Create a consistent representation of filters
        filter_str = json.dumps(filters or {}, sort_keys=True)
        
        # Generate hash
        cache_input = f"{normalized_query}|{filter_str}"
        return hashlib.md5(cache_input.encode()).hexdigest()
    
    def get(self, query: str, filters: Dict[str, Any] = None) -> Optional[Any]:
        """Get cached response if available and not expired."""
        cache_key = self._generate_cache_key(query, filters)
        
        if cache_key in self.cache:
            response, timestamp = self.cache[cache_key]
            
            # Check if cache entry is still valid
            if time.time() - timestamp < self.ttl_seconds:
                self.hits += 1
                logger.info(f"🎯 Cache HIT for query: {query[:50]}...")
                return response
            else:
                # Remove expired entry
                del self.cache[cache_key]
                logger.info(f"⏰ Cache EXPIRED for query: {query[:50]}...")
        
        self.misses += 1
        logger.info(f"❌ Cache MISS for query: {query[:50]}...")
        return None
    
    def set(self, query: str, response: Any, filters: Dict[str, Any] = None) -> None:
        """Cache a response."""
        cache_key = self._generate_cache_key(query, filters)
        self.cache[cache_key] = (response, time.time())
        logger.info(f"💾 Cached response for query: {query[:50]}...")
    
    def clear_expired(self) -> int:
        """Remove expired cache entries and return count of removed entries."""
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self.cache.items()
            if current_time - timestamp >= self.ttl_seconds
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.info(f"🧹 Cleared {len(expired_keys)} expired cache entries")
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.hits + self.misses
        hit_rate = (self.hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "total_entries": len(self.cache),
            "hits": self.hits,
            "misses": self.misses,
            "hit_rate_percent": round(hit_rate, 2),
            "ttl_minutes": self.ttl_seconds / 60
        }
    
    def clear_all(self) -> None:
        """Clear all cache entries."""
        count = len(self.cache)
        self.cache.clear()
        self.hits = 0
        self.misses = 0
        logger.info(f"🗑️ Cleared all {count} cache entries")

# Global cache instance
_global_cache = None

def get_cache() -> ResponseCache:
    """Get the global cache instance."""
    global _global_cache
    if _global_cache is None:
        from multi_tool_agent.config import AgentConfig
        ttl_minutes = getattr(AgentConfig, 'CACHE_TTL_MINUTES', 30)
        _global_cache = ResponseCache(ttl_minutes=ttl_minutes)
        logger.info(f"🚀 Initialized response cache with {ttl_minutes} minute TTL")
    return _global_cache

def cache_response(query: str, response: Any, filters: Dict[str, Any] = None) -> None:
    """Cache a response using the global cache."""
    cache = get_cache()
    cache.set(query, response, filters)

def get_cached_response(query: str, filters: Dict[str, Any] = None) -> Optional[Any]:
    """Get a cached response using the global cache."""
    cache = get_cache()
    return cache.get(query, filters)

def clear_expired_cache() -> int:
    """Clear expired entries from the global cache."""
    cache = get_cache()
    return cache.clear_expired()

def get_cache_stats() -> Dict[str, Any]:
    """Get statistics from the global cache."""
    cache = get_cache()
    return cache.get_stats()
