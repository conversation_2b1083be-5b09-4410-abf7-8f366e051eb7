"""
API Usage Monitor
Tracks API calls, rate limiting, and provides insights for optimization.
"""

import time
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json

logger = logging.getLogger(__name__)

@dataclass
class APICall:
    """Represents a single API call."""
    timestamp: float
    model: str
    task_type: str
    is_critical: bool
    success: bool
    error_type: Optional[str] = None
    response_time: Optional[float] = None
    tokens_used: Optional[int] = None

@dataclass
class APIStats:
    """API usage statistics."""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    rate_limited_calls: int = 0
    kimi_calls: int = 0
    fallback_calls: int = 0
    total_tokens: int = 0
    avg_response_time: float = 0.0
    calls_by_model: Dict[str, int] = field(default_factory=dict)
    calls_by_task: Dict[str, int] = field(default_factory=dict)
    errors_by_type: Dict[str, int] = field(default_factory=dict)

class APIMonitor:
    """Monitors API usage and provides optimization insights."""
    
    def __init__(self, window_minutes: int = 60):
        self.window_minutes = window_minutes
        self.window_seconds = window_minutes * 60
        self.calls_history = deque()  # Store APICall objects
        self.stats = APIStats()
        
        # Rate limiting tracking
        self.rpm_windows = defaultdict(deque)  # Track calls per minute for each model
        
    def record_call(self, 
                   model: str,
                   task_type: str = "unknown",
                   is_critical: bool = False,
                   success: bool = True,
                   error_type: Optional[str] = None,
                   response_time: Optional[float] = None,
                   tokens_used: Optional[int] = None):
        """Record an API call."""
        
        call = APICall(
            timestamp=time.time(),
            model=model,
            task_type=task_type,
            is_critical=is_critical,
            success=success,
            error_type=error_type,
            response_time=response_time,
            tokens_used=tokens_used
        )
        
        self.calls_history.append(call)
        self._update_stats(call)
        self._cleanup_old_calls()
        
        # Log the call
        status = "✅" if success else "❌"
        critical_flag = "🚨" if is_critical else ""
        logger.info(f"{status} {critical_flag} API call: {model} ({task_type}) - {response_time:.2f}s" if response_time else f"{status} {critical_flag} API call: {model} ({task_type})")
        
    def _update_stats(self, call: APICall):
        """Update statistics with new call."""
        self.stats.total_calls += 1
        
        if call.success:
            self.stats.successful_calls += 1
        else:
            self.stats.failed_calls += 1
            if call.error_type:
                self.stats.errors_by_type[call.error_type] = self.stats.errors_by_type.get(call.error_type, 0) + 1
                if "rate" in call.error_type.lower() or "429" in call.error_type:
                    self.stats.rate_limited_calls += 1
        
        # Track by model
        self.stats.calls_by_model[call.model] = self.stats.calls_by_model.get(call.model, 0) + 1
        
        # Track Kimi vs fallback
        if "kimi" in call.model.lower():
            self.stats.kimi_calls += 1
        else:
            self.stats.fallback_calls += 1
            
        # Track by task type
        self.stats.calls_by_task[call.task_type] = self.stats.calls_by_task.get(call.task_type, 0) + 1
        
        # Track tokens and response time
        if call.tokens_used:
            self.stats.total_tokens += call.tokens_used
        
        if call.response_time:
            # Update average response time
            total_time = self.stats.avg_response_time * (self.stats.total_calls - 1)
            self.stats.avg_response_time = (total_time + call.response_time) / self.stats.total_calls
    
    def _cleanup_old_calls(self):
        """Remove calls outside the monitoring window."""
        current_time = time.time()
        cutoff_time = current_time - self.window_seconds
        
        while self.calls_history and self.calls_history[0].timestamp < cutoff_time:
            self.calls_history.popleft()
    
    def get_rpm_for_model(self, model: str) -> int:
        """Get current requests per minute for a specific model."""
        current_time = time.time()
        cutoff_time = current_time - 60  # Last minute
        
        count = 0
        for call in reversed(self.calls_history):
            if call.timestamp < cutoff_time:
                break
            if call.model == model:
                count += 1
                
        return count
    
    def is_approaching_rate_limit(self, model: str, threshold: float = 0.8) -> bool:
        """Check if we're approaching rate limit for a model."""
        from multi_tool_agent.config import AgentConfig
        
        if "kimi" in model.lower():
            limit = AgentConfig.KIMI_RATE_LIMIT_RPM
            current_rpm = self.get_rpm_for_model(model)
            return current_rpm >= (limit * threshold)
        
        return False
    
    def get_optimization_suggestions(self) -> List[str]:
        """Get suggestions for optimizing API usage."""
        suggestions = []

        # Check Kimi usage (balanced strategy - should be 30-50% for strategic tasks)
        kimi_percentage = (self.stats.kimi_calls / max(self.stats.total_calls, 1)) * 100
        if kimi_percentage > 60:
            suggestions.append(f"🎯 Kimi usage is {kimi_percentage:.1f}% - consider using Gemini for simpler tasks")
        elif kimi_percentage < 20:
            suggestions.append(f"🧠 Kimi usage is {kimi_percentage:.1f}% - consider using Kimi for final output and delegation")
        else:
            suggestions.append(f"✅ Balanced Kimi usage - {kimi_percentage:.1f}% for strategic tasks")

        # Check rate limiting (should be rare with aggressive Gemini usage)
        if self.stats.rate_limited_calls > 0:
            rate_limit_percentage = (self.stats.rate_limited_calls / max(self.stats.total_calls, 1)) * 100
            suggestions.append(f"🚫 {rate_limit_percentage:.1f}% of calls were rate limited - use more Gemini fallbacks")

        # Check task distribution (should favor Gemini for most tasks)
        reasoning_tasks = self.stats.calls_by_task.get("reasoning", 0)
        tool_tasks = self.stats.calls_by_task.get("tool_execution", 0)
        search_tasks = self.stats.calls_by_task.get("search", 0)

        gemini_suitable_tasks = tool_tasks + search_tasks
        if reasoning_tasks > gemini_suitable_tasks:
            suggestions.append("⚡ Consider using unlimited Gemini for more non-critical tasks")

        # Check response times (Gemini is often faster)
        if self.stats.avg_response_time > 3.0:
            suggestions.append(f"⏱️ Average response time is {self.stats.avg_response_time:.1f}s - Gemini might be faster")

        # Check error rates
        error_rate = (self.stats.failed_calls / max(self.stats.total_calls, 1)) * 100
        if error_rate > 10:
            suggestions.append(f"❌ Error rate is {error_rate:.1f}% - investigate failures")

        # Gemini-specific suggestions
        gemini_percentage = (self.stats.fallback_calls / max(self.stats.total_calls, 1)) * 100
        if gemini_percentage > 80:
            suggestions.append(f"🚀 Excellent! {gemini_percentage:.1f}% using unlimited Gemini models")

        return suggestions
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current statistics."""
        self._cleanup_old_calls()
        
        # Calculate success rate
        success_rate = (self.stats.successful_calls / max(self.stats.total_calls, 1)) * 100
        
        # Get current RPM for Kimi
        kimi_rpm = self.get_rpm_for_model("novita/moonshotai/kimi-k2-instruct")
        
        return {
            "window_minutes": self.window_minutes,
            "total_calls": self.stats.total_calls,
            "success_rate": round(success_rate, 1),
            "kimi_calls": self.stats.kimi_calls,
            "fallback_calls": self.stats.fallback_calls,
            "rate_limited_calls": self.stats.rate_limited_calls,
            "current_kimi_rpm": kimi_rpm,
            "avg_response_time": round(self.stats.avg_response_time, 2),
            "total_tokens": self.stats.total_tokens,
            "calls_by_model": dict(self.stats.calls_by_model),
            "calls_by_task": dict(self.stats.calls_by_task),
            "errors_by_type": dict(self.stats.errors_by_type),
            "optimization_suggestions": self.get_optimization_suggestions()
        }
    
    def print_summary(self):
        """Print a summary of API usage."""
        stats = self.get_current_stats()
        
        print("\n" + "="*50)
        print("📊 API USAGE SUMMARY")
        print("="*50)
        print(f"📈 Total Calls: {stats['total_calls']}")
        print(f"✅ Success Rate: {stats['success_rate']}%")
        print(f"🧠 Kimi Calls: {stats['kimi_calls']}")
        print(f"⚡ Fallback Calls: {stats['fallback_calls']}")
        print(f"🚫 Rate Limited: {stats['rate_limited_calls']}")
        print(f"⏱️ Avg Response Time: {stats['avg_response_time']}s")
        print(f"🎯 Current Kimi RPM: {stats['current_kimi_rpm']}/10")
        
        if stats['optimization_suggestions']:
            print("\n💡 OPTIMIZATION SUGGESTIONS:")
            for suggestion in stats['optimization_suggestions']:
                print(f"  {suggestion}")
        
        print("="*50)

# Global monitor instance
_global_monitor = None

def get_api_monitor() -> APIMonitor:
    """Get the global API monitor."""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = APIMonitor(window_minutes=60)
        logger.info("🚀 Initialized API usage monitor")
    return _global_monitor

def record_api_call(model: str, 
                   task_type: str = "unknown",
                   is_critical: bool = False,
                   success: bool = True,
                   error_type: Optional[str] = None,
                   response_time: Optional[float] = None,
                   tokens_used: Optional[int] = None):
    """Convenience function to record an API call."""
    monitor = get_api_monitor()
    monitor.record_call(model, task_type, is_critical, success, error_type, response_time, tokens_used)
