#!/usr/bin/env python3
"""
Startup script for the Multi-Tool Agent with WebSocket support.
This is the correct server to run for frontend connectivity.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_environment():
    """Check if the environment is properly set up."""
    logger.info("🔍 Checking environment...")
    
    # Check if we're in the right directory
    if not Path("adk_server.py").exists():
        logger.error("❌ adk_server.py not found. Please run from the project root directory.")
        return False
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.warning("⚠️ Virtual environment may not be activated. Consider running:")
        logger.warning("   .venv\\Scripts\\activate  # Windows")
        logger.warning("   source .venv/bin/activate  # Linux/Mac")
    
    # Check for required environment variables
    required_vars = ["GOOGLE_API_KEY", "NOVITA_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("   Please check your .env file")
        return False
    
    logger.info("✅ Environment check passed")
    return True

def start_server():
    """Start the ADK server with WebSocket support."""
    logger.info("🚀 Starting Multi-Tool Agent Server with WebSocket support...")
    logger.info("📡 Frontend should connect to: http://localhost:8080")
    logger.info("📊 API monitoring available at: http://localhost:8080/api-stats")
    logger.info("🔧 Health check available at: http://localhost:8080/health")
    logger.info("")
    logger.info("🧠 Kimi K2 Strategy:")
    logger.info("   ✅ Final output generation")
    logger.info("   ✅ Agent delegation and orchestration")
    logger.info("   ✅ Tool calling decisions")
    logger.info("   ✅ Complex reasoning tasks")
    logger.info("")
    logger.info("⚡ Gemini Strategy:")
    logger.info("   ✅ Simple tool execution")
    logger.info("   ✅ Search operations")
    logger.info("   ✅ Data processing")
    logger.info("   ✅ Intermediate tasks")
    logger.info("")
    logger.info("🎯 Target: 30-50% Kimi usage for strategic tasks")
    logger.info("=" * 60)
    
    try:
        # Run the server
        subprocess.run([sys.executable, "adk_server.py"], check=True)
    except KeyboardInterrupt:
        logger.info("\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Server failed with exit code {e.returncode}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False
    
    return True

def main():
    """Main entry point."""
    print("🤖 Multi-Tool Agent Server Startup")
    print("=" * 40)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please fix the issues above.")
        sys.exit(1)
    
    print("\n📝 Important Notes:")
    print("   • Use this script instead of 'adk web --port 8001'")
    print("   • This server includes WebSocket support for the frontend")
    print("   • Rate limiting is optimized for Kimi K2's 10 RPM limit")
    print("   • Gemini models have unlimited usage")
    print("   • Monitor API usage at /api-stats endpoint")
    print("")
    
    # Start the server
    if not start_server():
        sys.exit(1)

if __name__ == "__main__":
    main()
