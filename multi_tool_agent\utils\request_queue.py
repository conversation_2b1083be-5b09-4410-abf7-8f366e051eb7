"""
Request Queue Manager for Rate Limiting
Handles concurrent requests to stay within API limits.
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, Callable, Awaitable
from dataclasses import dataclass
from collections import deque
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

@dataclass
class QueuedRequest:
    """Represents a queued request."""
    request_id: str
    task_type: str
    is_critical: bool
    timestamp: float
    future: asyncio.Future
    timeout: float = 30.0

class GlobalRequestQueue:
    """Global request queue to manage all API calls across the application."""
    
    def __init__(self, max_concurrent: int = 3, max_queue_size: int = 50):
        self.max_concurrent = max_concurrent
        self.max_queue_size = max_queue_size
        self.active_requests = 0
        self.request_queue = deque()
        self.queue_lock = asyncio.Lock()
        self.processing_task = None
        self.stats = {
            "total_requests": 0,
            "completed_requests": 0,
            "failed_requests": 0,
            "queued_requests": 0,
            "timeout_requests": 0
        }
        
    async def start_processing(self):
        """Start the background queue processor."""
        if self.processing_task is None or self.processing_task.done():
            self.processing_task = asyncio.create_task(self._process_queue())
            logger.info("🚀 Started global request queue processor")
    
    async def stop_processing(self):
        """Stop the background queue processor."""
        if self.processing_task and not self.processing_task.done():
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
            logger.info("🛑 Stopped global request queue processor")
    
    async def _process_queue(self):
        """Background task to process queued requests."""
        while True:
            try:
                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting
                
                async with self.queue_lock:
                    # Remove expired requests
                    current_time = time.time()
                    expired_requests = []
                    
                    while self.request_queue:
                        request = self.request_queue[0]
                        if current_time - request.timestamp > request.timeout:
                            expired_request = self.request_queue.popleft()
                            expired_requests.append(expired_request)
                        else:
                            break
                    
                    # Handle expired requests
                    for request in expired_requests:
                        if not request.future.done():
                            request.future.set_exception(TimeoutError(f"Request {request.request_id} timed out"))
                        self.stats["timeout_requests"] += 1
                        logger.warning(f"⏰ Request {request.request_id} timed out after {request.timeout}s")
                    
                    # Process new requests if we have capacity
                    while (self.active_requests < self.max_concurrent and 
                           self.request_queue and 
                           not self.request_queue[0].future.done()):
                        
                        request = self.request_queue.popleft()
                        self.active_requests += 1
                        
                        # Create task to handle the request
                        asyncio.create_task(self._handle_request(request))
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in queue processor: {e}")
                await asyncio.sleep(1)  # Wait before retrying
    
    async def _handle_request(self, request: QueuedRequest):
        """Handle a single request."""
        try:
            logger.info(f"🔄 Processing request {request.request_id} ({request.task_type})")
            # The actual request handling is done by the caller
            # This just manages the concurrency
            
        except Exception as e:
            logger.error(f"❌ Error handling request {request.request_id}: {e}")
            if not request.future.done():
                request.future.set_exception(e)
            self.stats["failed_requests"] += 1
        finally:
            self.active_requests -= 1
    
    async def queue_request(self, 
                          request_id: str, 
                          task_type: str = "unknown",
                          is_critical: bool = False,
                          timeout: float = 30.0) -> asyncio.Future:
        """Queue a request for processing."""
        
        async with self.queue_lock:
            # Check queue size
            if len(self.request_queue) >= self.max_queue_size:
                raise RuntimeError(f"Request queue full ({self.max_queue_size} requests)")
            
            # Create future for the result
            future = asyncio.Future()
            
            # Create queued request
            queued_request = QueuedRequest(
                request_id=request_id,
                task_type=task_type,
                is_critical=is_critical,
                timestamp=time.time(),
                future=future,
                timeout=timeout
            )
            
            # Add to queue (prioritize critical requests)
            if is_critical:
                # Insert at the beginning for critical requests
                self.request_queue.appendleft(queued_request)
                logger.info(f"🚨 Queued CRITICAL request {request_id} (position: 1)")
            else:
                self.request_queue.append(queued_request)
                logger.info(f"📝 Queued request {request_id} (position: {len(self.request_queue)})")
            
            self.stats["total_requests"] += 1
            self.stats["queued_requests"] += 1
            
            return future
    
    @asynccontextmanager
    async def acquire_slot(self, 
                          request_id: str, 
                          task_type: str = "unknown",
                          is_critical: bool = False,
                          timeout: float = 30.0):
        """Context manager to acquire a processing slot."""
        
        # Start processing if not already started
        await self.start_processing()
        
        # Check if we can process immediately
        if self.active_requests < self.max_concurrent:
            async with self.queue_lock:
                if self.active_requests < self.max_concurrent:
                    self.active_requests += 1
                    logger.info(f"🚀 Immediate processing for {request_id} ({task_type})")
                    try:
                        yield
                        self.stats["completed_requests"] += 1
                    finally:
                        self.active_requests -= 1
                    return
        
        # Need to queue the request
        future = await self.queue_request(request_id, task_type, is_critical, timeout)
        
        try:
            # Wait for our turn
            await future
            logger.info(f"✅ Request {request_id} ready for processing")
            yield
            self.stats["completed_requests"] += 1
        except Exception as e:
            logger.error(f"❌ Request {request_id} failed: {e}")
            self.stats["failed_requests"] += 1
            raise
        finally:
            self.stats["queued_requests"] -= 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get queue statistics."""
        return {
            **self.stats,
            "active_requests": self.active_requests,
            "queue_length": len(self.request_queue),
            "queue_capacity": self.max_queue_size,
            "max_concurrent": self.max_concurrent
        }

# Global queue instance
_global_queue = None

def get_global_queue() -> GlobalRequestQueue:
    """Get the global request queue."""
    global _global_queue
    if _global_queue is None:
        from multi_tool_agent.config import AgentConfig
        max_concurrent = getattr(AgentConfig, 'MAX_CONCURRENT_SEARCHES', 3)
        max_queue_size = getattr(AgentConfig, 'MAX_QUEUE_SIZE', 50)
        _global_queue = GlobalRequestQueue(max_concurrent, max_queue_size)
        logger.info(f"🚀 Initialized global request queue (max_concurrent={max_concurrent}, max_queue={max_queue_size})")
    return _global_queue

async def queue_request(request_id: str, 
                       task_type: str = "unknown",
                       is_critical: bool = False,
                       timeout: float = 30.0):
    """Convenience function to queue a request."""
    queue = get_global_queue()
    async with queue.acquire_slot(request_id, task_type, is_critical, timeout):
        yield
