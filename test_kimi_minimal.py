#!/usr/bin/env python3
"""
Minimal test for Kimi K2 integration - bypassing complex agent workflows.
"""

import asyncio
import logging
import sys
import os

# Add the multi_tool_agent directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'multi_tool_agent'))

from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types
from config import AgentConfig

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Simple Kimi agent without tools
simple_kimi_agent = Agent(
    name="SimpleKimiAgent",
    model=LiteLlm(model=AgentConfig.KIMI_MODEL),
    description="Simple Kimi K2 agent for testing.",
    instruction=(
        "You are a helpful assistant powered by Kimi K2. "
        "Answer questions clearly and concisely. "
        "For activity-related questions, provide helpful general advice."
    ),
)

async def test_simple_kimi():
    """Test Kimi K2 with a simple agent (no tools)."""
    logger.info("🧪 Testing simple Kimi K2 agent...")
    
    try:
        # Session setup
        session_service = InMemorySessionService()
        APP_NAME, USER_ID, SESSION_ID = "simple_kimi_test", "user1", "session1"
        await session_service.create_session(
            app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
        )
        
        # Runner
        runner = Runner(agent=simple_kimi_agent, app_name=APP_NAME, session_service=session_service)
        logger.info(f"✅ Runner created for {runner.agent.name}")
        
        # Test query
        query = "What are 3 benefits of swimming for children?"
        logger.info(f"📝 Query: {query}")
        
        content = types.Content(role='user', parts=[types.Part(text=query)])
        
        response_text = "No response received."
        
        async for event in runner.run_async(
            user_id=USER_ID,
            session_id=SESSION_ID,
            new_message=content
        ):
            if event.is_final_response() and event.content and event.content.parts:
                response_text = event.content.parts[0].text.strip()
                break
        
        logger.info(f"🤖 Kimi Response:\n{response_text}")
        
        # Validation - check for quality response about swimming/water activities
        keywords = ["swimming", "water", "benefit", "exercise", "fitness", "safety", "confidence", "development"]
        found_keywords = sum(1 for keyword in keywords if keyword in response_text.lower())

        if len(response_text) > 50 and found_keywords >= 2:
            logger.info("✅ Simple Kimi K2 test PASSED!")
            return True
        else:
            logger.error(f"❌ Simple Kimi K2 test FAILED - Response too short or lacks relevant content (found {found_keywords} keywords)")
            return False
            
    except Exception as e:
        logger.error(f"❌ Simple Kimi K2 test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_simple_kimi())
    print(f"\n{'🎉 SUCCESS' if success else '❌ FAILED'}")
