#!/usr/bin/env python3
"""
Start Server with Model Management
Handles model downloading and server startup with proper model initialization.
"""

import os
import sys
import asyncio
import logging
import subprocess
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def check_and_download_models():
    """Check if models exist and download if needed."""
    try:
        from multi_tool_agent.utils.model_manager import get_model_manager
        
        manager = get_model_manager()
        
        # Check current status
        status = manager.check_models_exist()
        cache_info = manager.get_cache_info()
        
        logger.info("📊 Model cache status:")
        logger.info(f"   Cache directory: {cache_info['cache_dir']}")
        logger.info(f"   Cache size: {cache_info['cache_size_mb']} MB")
        logger.info(f"   Models: {status}")
        
        missing_models = [k for k, v in status.items() if not v]
        
        if missing_models:
            logger.warning(f"⚠️ Missing models: {missing_models}")
            logger.info("📥 Models need to be downloaded for optimal performance")
            logger.info("💡 Run 'python download_models.py' to download models")
            logger.info("🚀 Starting server with simple search fallback...")
            return False
        else:
            logger.info("✅ All models found in cache")
            
            # Try to load models
            logger.info("🔄 Loading models...")
            success = await manager.ensure_models_ready()
            
            if success:
                logger.info("✅ Models loaded successfully - advanced search enabled")
                return True
            else:
                logger.warning("⚠️ Failed to load models - using simple search fallback")
                return False
                
    except Exception as e:
        logger.error(f"❌ Model check failed: {e}")
        logger.info("🚀 Starting server with simple search fallback...")
        return False

def start_server():
    """Start the ADK server."""
    logger.info("🚀 Starting Multi-Tool Agent Server...")
    
    try:
        # Run the server
        subprocess.run([sys.executable, "adk_server.py"], check=True)
    except KeyboardInterrupt:
        logger.info("\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Server failed with exit code {e.returncode}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False
    
    return True

async def main():
    """Main startup process."""
    print("🤖 Multi-Tool Agent with Model Management")
    print("=" * 50)
    
    # Check environment
    if not Path("adk_server.py").exists():
        logger.error("❌ adk_server.py not found. Please run from the project root directory.")
        sys.exit(1)
    
    # Check if models should be preloaded
    preload = os.environ.get("PRELOAD_MODELS", "false").lower() == "true"
    
    if preload:
        logger.info("🔄 PRELOAD_MODELS=true - checking and loading models...")
        models_ready = await check_and_download_models()
        
        if models_ready:
            logger.info("🎯 Advanced search with 3-stage reranking enabled")
        else:
            logger.info("⚡ Simple search fallback enabled")
    else:
        logger.info("⚡ PRELOAD_MODELS=false - models will be loaded on demand")
        logger.info("💡 Set PRELOAD_MODELS=true to enable advanced search on startup")
    
    print("\n📝 Configuration:")
    print(f"   Model preloading: {'enabled' if preload else 'disabled'}")
    print(f"   Cache directory: {os.environ.get('MODEL_CACHE_DIR', './models')}")
    print(f"   Server port: 8080")
    print(f"   WebSocket support: enabled")
    print("")
    
    # Start server
    start_server()

def show_help():
    """Show help information."""
    print("""
🤖 Multi-Tool Agent Startup Script

This script manages model downloading and server startup.

Usage:
    python start_with_models.py [options]

Options:
    -h, --help     Show this help message
    --download     Download models before starting server
    --check        Check model status and exit

Environment Variables:
    PRELOAD_MODELS=true    Load models on startup (enables advanced search)
    MODEL_CACHE_DIR=path   Directory to store models (default: ./models)

Model Management:
    • Models are cached locally for production use
    • First run may take time to download models (~2GB)
    • Subsequent runs use cached models (fast startup)
    • Advanced search requires models to be loaded
    • Simple search works without models (fallback)

Examples:
    # Start with model preloading
    PRELOAD_MODELS=true python start_with_models.py
    
    # Start with simple search (fast)
    python start_with_models.py
    
    # Download models separately
    python download_models.py
""")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        
        if arg in ["-h", "--help", "help"]:
            show_help()
            sys.exit(0)
        elif arg == "--download":
            print("📥 Downloading models...")
            subprocess.run([sys.executable, "download_models.py"])
            sys.exit(0)
        elif arg == "--check":
            async def check_only():
                await check_and_download_models()
            asyncio.run(check_only())
            sys.exit(0)
        else:
            print(f"❌ Unknown option: {arg}")
            print("Use --help for usage information")
            sys.exit(1)
    
    asyncio.run(main())
