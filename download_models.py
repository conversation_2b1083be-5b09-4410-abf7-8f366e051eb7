#!/usr/bin/env python3
"""
Model Download Script
Downloads and caches embedding models locally for production use.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_requirements():
    """Check if required packages are installed."""
    try:
        import fastembed
        from qdrant_client import AsyncQdrantClient
        logger.info("✅ Required packages found")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing required package: {e}")
        logger.error("Please install requirements: pip install -r requirements.txt")
        return False

async def main():
    """Main download process."""
    logger.info("🚀 Starting model download process...")
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    try:
        # Import model manager
        from multi_tool_agent.utils.model_manager import get_model_manager
        from multi_tool_agent.config import AgentConfig
        
        manager = get_model_manager()
        
        # Show current status
        logger.info("📊 Current model cache status:")
        cache_info = manager.get_cache_info()
        for key, value in cache_info.items():
            logger.info(f"   {key}: {value}")
        
        # Check what models we need
        status = manager.check_models_exist()
        missing_models = [k for k, v in status.items() if not v]
        
        if not missing_models:
            logger.info("✅ All models already cached locally!")
            logger.info("💡 To force re-download, delete the models directory and run again")
            return
        
        logger.info(f"📥 Need to download: {missing_models}")
        logger.info(f"📁 Cache directory: {manager.cache_dir}")
        logger.info(f"🔧 Models to download:")
        logger.info(f"   Dense: {AgentConfig.EMBEDDING_MODEL}")
        logger.info(f"   Sparse: {AgentConfig.SPARSE_EMBEDDING_MODEL}")
        logger.info(f"   ColBERT: {AgentConfig.COLBERT_MODEL}")
        
        # Confirm download
        response = input("\n🤔 Proceed with download? This may take several minutes and use ~2GB disk space. (y/N): ")
        if response.lower() not in ['y', 'yes']:
            logger.info("❌ Download cancelled by user")
            return
        
        # Start download
        start_time = time.time()
        logger.info("📥 Starting download process...")
        
        success = await manager.download_models()
        
        if success:
            total_time = time.time() - start_time
            logger.info(f"✅ Download completed successfully in {total_time:.2f}s")
            
            # Show final cache info
            final_info = manager.get_cache_info()
            logger.info("📊 Final cache status:")
            logger.info(f"   Cache size: {final_info['cache_size_mb']} MB")
            logger.info(f"   Files: {final_info['file_count']}")
            logger.info(f"   Models ready: {final_info['models_loaded']}")
            
            # Test model loading
            logger.info("🧪 Testing model loading...")
            embedders = manager.get_embedders()
            if all(e is not None for e in embedders):
                logger.info("✅ All models loaded successfully!")
                logger.info("🚀 Your agent is now ready for production with local models")
            else:
                logger.warning("⚠️ Some models failed to load")
        else:
            logger.error("❌ Download failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n❌ Download interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Download failed with error: {e}")
        sys.exit(1)

def show_usage():
    """Show usage information."""
    print("""
🤖 Model Download Script

This script downloads and caches embedding models locally for production use.

Usage:
    python download_models.py

Environment Variables:
    MODEL_CACHE_DIR    - Directory to store models (default: ./models)
    PRELOAD_MODELS     - Set to 'true' to preload on startup (default: false)

Models that will be downloaded:
    • BAAI/bge-base-en-v1.5 (Dense embeddings, ~500MB)
    • Qdrant/bm25 (Sparse embeddings, ~50MB)  
    • colbert-ir/colbertv2.0 (ColBERT reranking, ~1GB)

Total size: ~2GB

After download:
    • Models will be cached locally
    • Agent startup will be much faster
    • No internet required for model loading
    • Production ready deployment
""")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        show_usage()
        sys.exit(0)
    
    print("🤖 Embedding Model Download Script")
    print("=" * 40)
    
    asyncio.run(main())
