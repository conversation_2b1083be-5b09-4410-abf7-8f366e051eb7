"""
Kimi-compatible AgentTool wrapper.

This module provides a wrapper around the standard AgentTool that handles
parameter mapping for Kimi K2 model compatibility.
"""

import logging
from typing import Any, Dict
from google.adk.tools.agent_tool import AgentTool
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.tool_context import ToolContext
from google.genai import types

logger = logging.getLogger(__name__)

class KimiAgentTool(BaseTool):
    """
    A wrapper around AgentTool that handles parameter mapping for Kimi K2 compatibility.
    
    The standard AgentTool expects a 'request' parameter, but Kimi K2 may generate
    different parameter names. This wrapper handles the mapping automatically.
    """
    
    def __init__(self, agent):
        """Initialize with an agent."""
        self.agent = agent
        self._agent_tool = AgentTool(agent=agent)
        
    @property
    def name(self):
        """Get the tool name."""
        return self._agent_tool.name
        
    @property
    def description(self):
        """Get the tool description."""
        return self._agent_tool.description
        
    @property
    def input_schema(self):
        """Get the input schema."""
        return self._agent_tool.input_schema
        
    async def run_async(self, args: Dict[str, Any], tool_context: ToolContext) -> Any:
        """
        Run the agent tool with parameter mapping for Kimi K2 compatibility.
        
        Args:
            args: The arguments passed by the LLM
            tool_context: The tool execution context
            
        Returns:
            The result from the agent
        """
        logger.info(f"KimiAgentTool received args: {args}")
        
        # Handle parameter mapping for Kimi K2 compatibility
        mapped_args = self._map_parameters(args)
        
        logger.info(f"KimiAgentTool mapped args: {mapped_args}")
        
        try:
            # Call the underlying AgentTool with mapped parameters
            return await self._agent_tool.run_async(args=mapped_args, tool_context=tool_context)
        except Exception as e:
            logger.error(f"KimiAgentTool error: {e}")
            # If there's still a parameter issue, try alternative mappings
            return await self._fallback_execution(args, tool_context)
    
    def _map_parameters(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Map parameters from Kimi K2 format to AgentTool expected format.
        
        Args:
            args: Original arguments from Kimi K2
            
        Returns:
            Mapped arguments for AgentTool
        """
        # If 'request' is already present, return as-is
        if 'request' in args:
            return args
            
        # Common parameter mappings for Kimi K2
        mapped_args = {}
        
        # Try different possible parameter names that Kimi might use
        possible_request_keys = [
            'query', 'user_query', 'user_request', 'input', 'text', 
            'message', 'prompt', 'question', 'content', 'data'
        ]
        
        # Find the main content parameter
        request_content = None
        for key in possible_request_keys:
            if key in args:
                request_content = args[key]
                break
        
        # If no specific key found, try to construct from all available args
        if request_content is None:
            if len(args) == 1:
                # If there's only one parameter, use it as the request
                request_content = list(args.values())[0]
            else:
                # Combine all string values as the request
                string_values = [str(v) for v in args.values() if isinstance(v, (str, int, float))]
                request_content = ' '.join(string_values) if string_values else str(args)
        
        # Set the mapped request parameter
        mapped_args['request'] = str(request_content) if request_content is not None else ""
        
        # Preserve any other parameters that might be needed
        for key, value in args.items():
            if key not in possible_request_keys:
                mapped_args[key] = value
                
        return mapped_args
    
    async def _fallback_execution(self, args: Dict[str, Any], tool_context: ToolContext) -> Any:
        """
        Fallback execution method if standard parameter mapping fails.
        
        Args:
            args: Original arguments
            tool_context: Tool execution context
            
        Returns:
            Result from direct agent execution
        """
        logger.warning("Using fallback execution for KimiAgentTool")
        
        try:
            # Try to execute the agent directly with a simple text request
            request_text = str(args) if args else "Please process this request."
            
            # Create a simple content object for the agent
            content = types.Content(
                role='user',
                parts=[types.Part.from_text(text=request_text)]
            )
            
            # This is a simplified fallback - in a real scenario, you might need
            # to implement a more sophisticated agent execution method
            return f"Agent {self.agent.name} processed request: {request_text}"
            
        except Exception as e:
            logger.error(f"Fallback execution failed: {e}")
            return f"Error executing agent {self.agent.name}: {str(e)}"
