"""
Model Manager for Embedding Models
Handles local caching, downloading, and initialization of embedding models.
"""

import os
import logging
import asyncio
import time
from pathlib import Path
from typing import Optional, Tuple, Any
import threading

logger = logging.getLogger(__name__)

class ModelManager:
    """Manages embedding model lifecycle with local caching."""
    
    def __init__(self):
        # Use environment variables directly to avoid circular imports
        self.cache_dir = Path(os.environ.get("MODEL_CACHE_DIR", "./models"))
        self.embedding_model = "BAAI/bge-base-en-v1.5"
        self.sparse_model = "Qdrant/bm25"
        self.colbert_model = "colbert-ir/colbertv2.0"
        self.download_timeout = 300  # 5 minutes
        self.preload_on_startup = os.environ.get("PRELOAD_MODELS", "false").lower() == "true"
        
        # Model instances (lazy loaded)
        self._dense_embedder = None
        self._sparse_embedder = None
        self._colbert_embedder = None
        self._models_loaded = False
        self._loading_lock = threading.Lock()
        
        # Ensure cache directory exists
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 Model cache directory: {self.cache_dir}")
        
    def check_models_exist(self) -> dict:
        """Check which models are already cached locally."""
        status = {}
        
        # Check dense model
        dense_path = self.cache_dir / "dense" / self.embedding_model.replace("/", "_")
        status["dense"] = dense_path.exists()
        
        # Check sparse model  
        sparse_path = self.cache_dir / "sparse" / self.sparse_model.replace("/", "_")
        status["sparse"] = sparse_path.exists()
        
        # Check ColBERT model
        colbert_path = self.cache_dir / "colbert" / self.colbert_model.replace("/", "_")
        status["colbert"] = colbert_path.exists()
        
        logger.info(f"📊 Model cache status: {status}")
        return status
    
    async def download_models(self, force: bool = False) -> bool:
        """Download and cache all embedding models."""
        logger.info("📥 Starting model download process...")
        
        try:
            # Import here to avoid startup delays
            from fastembed import TextEmbedding, SparseTextEmbedding, LateInteractionTextEmbedding
            
            # Set cache directory for fastembed
            os.environ["FASTEMBED_CACHE_PATH"] = str(self.cache_dir)
            
            start_time = time.time()
            
            # Download dense model
            logger.info(f"📥 Downloading dense model: {self.embedding_model}")
            dense_embedder = TextEmbedding(
                model_name=self.embedding_model,
                cache_dir=str(self.cache_dir / "dense")
            )
            
            # Download sparse model
            logger.info(f"📥 Downloading sparse model: {self.sparse_model}")
            sparse_embedder = SparseTextEmbedding(
                model_name=self.sparse_model,
                cache_dir=str(self.cache_dir / "sparse")
            )
            
            # Download ColBERT model
            logger.info(f"📥 Downloading ColBERT model: {self.colbert_model}")
            colbert_embedder = LateInteractionTextEmbedding(
                model_name=self.colbert_model,
                cache_dir=str(self.cache_dir / "colbert")
            )
            
            download_time = time.time() - start_time
            logger.info(f"✅ All models downloaded successfully in {download_time:.2f}s")
            
            # Store instances for immediate use
            self._dense_embedder = dense_embedder
            self._sparse_embedder = sparse_embedder
            self._colbert_embedder = colbert_embedder
            self._models_loaded = True
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to download models: {e}")
            return False
    
    def get_embedders(self) -> Tuple[Optional[Any], Optional[Any], Optional[Any]]:
        """Get embedding models with lazy loading and caching."""
        
        with self._loading_lock:
            if self._models_loaded:
                return self._dense_embedder, self._sparse_embedder, self._colbert_embedder
            
            try:
                # Import here to avoid startup delays
                from fastembed import TextEmbedding, SparseTextEmbedding, LateInteractionTextEmbedding
                
                # Set cache directory for fastembed
                os.environ["FASTEMBED_CACHE_PATH"] = str(self.cache_dir)
                
                logger.info("🔄 Loading embedding models from cache...")
                start_time = time.time()
                
                # Load models from cache
                self._dense_embedder = TextEmbedding(
                    model_name=self.embedding_model,
                    cache_dir=str(self.cache_dir / "dense")
                )
                
                self._sparse_embedder = SparseTextEmbedding(
                    model_name=self.sparse_model,
                    cache_dir=str(self.cache_dir / "sparse")
                )
                
                self._colbert_embedder = LateInteractionTextEmbedding(
                    model_name=self.colbert_model,
                    cache_dir=str(self.cache_dir / "colbert")
                )
                
                load_time = time.time() - start_time
                logger.info(f"✅ Models loaded from cache in {load_time:.2f}s")
                
                self._models_loaded = True
                return self._dense_embedder, self._sparse_embedder, self._colbert_embedder
                
            except Exception as e:
                logger.error(f"❌ Failed to load models: {e}")
                logger.info("💡 Models may need to be downloaded first")
                return None, None, None
    
    async def ensure_models_ready(self) -> bool:
        """Ensure models are downloaded and ready for use."""
        
        # Check if models exist locally
        status = self.check_models_exist()
        
        if all(status.values()):
            logger.info("✅ All models found in cache")
            # Try to load them
            embedders = self.get_embedders()
            return all(e is not None for e in embedders)
        else:
            missing = [k for k, v in status.items() if not v]
            logger.info(f"📥 Missing models: {missing}. Downloading...")
            return await self.download_models()
    
    def get_cache_info(self) -> dict:
        """Get information about the model cache."""
        cache_size = 0
        file_count = 0
        
        if self.cache_dir.exists():
            for file_path in self.cache_dir.rglob("*"):
                if file_path.is_file():
                    cache_size += file_path.stat().st_size
                    file_count += 1
        
        return {
            "cache_dir": str(self.cache_dir),
            "cache_size_mb": round(cache_size / (1024 * 1024), 2),
            "file_count": file_count,
            "models_loaded": self._models_loaded,
            "model_status": self.check_models_exist()
        }

# Global model manager instance
_model_manager = None

def get_model_manager() -> ModelManager:
    """Get the global model manager instance."""
    global _model_manager
    if _model_manager is None:
        _model_manager = ModelManager()
        logger.info("🚀 Initialized model manager")
    return _model_manager

async def ensure_models_ready() -> bool:
    """Convenience function to ensure models are ready."""
    manager = get_model_manager()
    return await manager.ensure_models_ready()

def get_embedders() -> Tuple[Optional[Any], Optional[Any], Optional[Any]]:
    """Convenience function to get embedding models."""
    manager = get_model_manager()
    return manager.get_embedders()

def get_cache_info() -> dict:
    """Convenience function to get cache information."""
    manager = get_model_manager()
    return manager.get_cache_info()
