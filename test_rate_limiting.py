#!/usr/bin/env python3
"""
Test script to verify rate limiting and hybrid model functionality.
"""

import asyncio
import time
import logging
from multi_tool_agent.utils.rate_limiter import get_hybrid_manager
from multi_tool_agent.utils.api_monitor import get_api_monitor, record_api_call
from multi_tool_agent.utils.request_queue import get_global_queue
from multi_tool_agent.config import AgentConfig

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def simulate_api_call(model: str, task_type: str, is_critical: bool = False, delay: float = 0.5):
    """Simulate an API call with response time."""
    start_time = time.time()
    
    try:
        # Simulate processing time
        await asyncio.sleep(delay)
        
        # Simulate success/failure (95% success rate)
        import random
        success = random.random() > 0.05
        
        if not success:
            raise Exception("Simulated API error")
            
        response_time = time.time() - start_time
        
        # Record the call
        record_api_call(
            model=model,
            task_type=task_type,
            is_critical=is_critical,
            success=True,
            response_time=response_time,
            tokens_used=random.randint(100, 1000)
        )
        
        logger.info(f"✅ API call completed: {model} ({task_type}) - {response_time:.2f}s")
        return f"Response from {model}"
        
    except Exception as e:
        response_time = time.time() - start_time
        record_api_call(
            model=model,
            task_type=task_type,
            is_critical=is_critical,
            success=False,
            error_type=type(e).__name__,
            response_time=response_time
        )
        logger.error(f"❌ API call failed: {model} ({task_type}) - {e}")
        raise

async def test_hybrid_model_selection():
    """Test hybrid model selection logic - should heavily favor unlimited Gemini."""
    logger.info("🧪 Testing hybrid model selection with unlimited Gemini...")

    manager = get_hybrid_manager()

    # Test different task types - should mostly use Gemini since it's unlimited
    test_cases = [
        ("reasoning", True),      # Should use Kimi (critical reasoning)
        ("orchestration", True),  # Should use Kimi (critical orchestration)
        ("reasoning", False),     # Should use Gemini (non-critical reasoning)
        ("complex_analysis", True), # Should use Gemini (not in critical list)
        ("tool_execution", False), # Should use Gemini (unlimited)
        ("search", False),        # Should use Gemini (unlimited)
        ("formatting", False),    # Should use Gemini (unlimited)
        ("unknown", False)        # Should use Gemini (unlimited)
    ]

    kimi_count = 0
    gemini_count = 0

    for task_type, is_critical in test_cases:
        model = manager.get_model_for_task(task_type, is_critical)
        should_use_kimi = await manager.should_use_kimi(task_type, is_critical)

        if should_use_kimi:
            kimi_count += 1
        else:
            gemini_count += 1

        logger.info(f"📋 Task: {task_type} (critical={is_critical}) -> Model: {model}, Use Kimi: {should_use_kimi}")

    logger.info(f"🎯 Model distribution: Kimi={kimi_count}, Gemini={gemini_count}")
    logger.info(f"✅ Gemini usage: {(gemini_count/len(test_cases)*100):.1f}% (should be >75%)")

async def test_rate_limiting():
    """Test rate limiting functionality."""
    logger.info("🧪 Testing rate limiting...")
    
    manager = get_hybrid_manager()
    
    # Simulate rapid Kimi requests
    tasks = []
    for i in range(15):  # More than 10 RPM limit
        task = simulate_api_call(
            model=AgentConfig.KIMI_MODEL,
            task_type="reasoning",
            is_critical=True,
            delay=0.1
        )
        tasks.append(task)
        
        # Small delay between requests
        await asyncio.sleep(0.1)
    
    # Execute all tasks concurrently
    start_time = time.time()
    results = await asyncio.gather(*tasks, return_exceptions=True)
    total_time = time.time() - start_time
    
    # Count successes and failures
    successes = sum(1 for r in results if not isinstance(r, Exception))
    failures = len(results) - successes
    
    logger.info(f"📊 Rate limiting test results:")
    logger.info(f"   Total requests: {len(tasks)}")
    logger.info(f"   Successful: {successes}")
    logger.info(f"   Failed: {failures}")
    logger.info(f"   Total time: {total_time:.2f}s")
    logger.info(f"   Effective RPM: {(len(tasks) / total_time) * 60:.1f}")

async def test_request_queue():
    """Test request queue functionality."""
    logger.info("🧪 Testing request queue...")
    
    queue = get_global_queue()
    await queue.start_processing()
    
    # Simulate concurrent requests
    async def queued_request(request_id: str, task_type: str, is_critical: bool = False):
        async with queue.acquire_slot(request_id, task_type, is_critical):
            await simulate_api_call(
                model=AgentConfig.KIMI_MODEL if is_critical else AgentConfig.FALLBACK_MODEL,
                task_type=task_type,
                is_critical=is_critical,
                delay=0.2
            )
    
    # Create mix of critical and non-critical requests
    tasks = []
    for i in range(10):
        is_critical = i % 3 == 0  # Every 3rd request is critical
        task_type = "reasoning" if is_critical else "search"
        request_id = f"test_request_{i}"
        
        task = queued_request(request_id, task_type, is_critical)
        tasks.append(task)
    
    start_time = time.time()
    await asyncio.gather(*tasks, return_exceptions=True)
    total_time = time.time() - start_time
    
    logger.info(f"📊 Queue test completed in {total_time:.2f}s")
    logger.info(f"📈 Queue stats: {queue.get_stats()}")
    
    await queue.stop_processing()

async def test_monitoring():
    """Test monitoring functionality."""
    logger.info("🧪 Testing monitoring...")
    
    monitor = get_api_monitor()
    
    # Generate some test data
    models = [AgentConfig.KIMI_MODEL, AgentConfig.FALLBACK_MODEL, AgentConfig.CHEAP_MODEL]
    task_types = ["reasoning", "search", "tool_execution", "formatting"]
    
    for i in range(20):
        model = models[i % len(models)]
        task_type = task_types[i % len(task_types)]
        is_critical = task_type == "reasoning"
        
        await simulate_api_call(model, task_type, is_critical, delay=0.1)
        await asyncio.sleep(0.05)  # Small delay between calls
    
    # Print monitoring summary
    monitor.print_summary()
    
    # Get optimization suggestions
    stats = monitor.get_current_stats()
    if stats['optimization_suggestions']:
        logger.info("💡 Optimization suggestions:")
        for suggestion in stats['optimization_suggestions']:
            logger.info(f"   {suggestion}")

async def main():
    """Run all tests."""
    logger.info("🚀 Starting rate limiting and hybrid model tests...")
    
    try:
        # Test 1: Hybrid model selection
        await test_hybrid_model_selection()
        await asyncio.sleep(1)
        
        # Test 2: Rate limiting
        await test_rate_limiting()
        await asyncio.sleep(2)
        
        # Test 3: Request queue
        await test_request_queue()
        await asyncio.sleep(1)
        
        # Test 4: Monitoring
        await test_monitoring()
        
        logger.info("✅ All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
