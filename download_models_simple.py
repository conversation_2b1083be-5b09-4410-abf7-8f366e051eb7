#!/usr/bin/env python3
"""
Simple Model Download Script
Downloads embedding models without complex dependencies.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Model configuration (hardcoded to avoid imports)
# Note: Sparse embeddings (BM25) are pre-processed and stored in Qdrant
MODELS = {
    "dense": "BAAI/bge-base-en-v1.5",
    "colbert": "colbert-ir/colbertv2.0"
}

def check_requirements():
    """Check if fastembed is installed."""
    try:
        import fastembed
        logger.info("✅ fastembed package found")
        return True
    except ImportError:
        logger.error("❌ fastembed not found. Install with: pip install fastembed")
        return False

def download_models(cache_dir: str = "./models"):
    """Download all embedding models."""
    cache_path = Path(cache_dir)
    cache_path.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"📁 Using cache directory: {cache_path}")
    
    # Set environment variable for fastembed
    os.environ["FASTEMBED_CACHE_PATH"] = str(cache_path)
    
    try:
        from fastembed import TextEmbedding, LateInteractionTextEmbedding

        total_start = time.time()

        # Download dense model
        logger.info(f"📥 Downloading dense model: {MODELS['dense']}")
        start = time.time()
        dense_embedder = TextEmbedding(model_name=MODELS["dense"])
        logger.info(f"✅ Dense model downloaded in {time.time() - start:.2f}s")

        # Download ColBERT model
        logger.info(f"📥 Downloading ColBERT model: {MODELS['colbert']}")
        start = time.time()
        colbert_embedder = LateInteractionTextEmbedding(model_name=MODELS["colbert"])
        logger.info(f"✅ ColBERT model downloaded in {time.time() - start:.2f}s")
        
        total_time = time.time() - total_start
        logger.info(f"🎉 All models downloaded successfully in {total_time:.2f}s")
        
        # Test embeddings
        logger.info("🧪 Testing embeddings...")
        test_text = "swimming lessons for children"

        dense_result = list(dense_embedder.embed([test_text]))[0]
        colbert_result = list(colbert_embedder.embed([test_text]))[0]

        logger.info(f"✅ Dense embedding: {len(dense_result)} dimensions")
        logger.info(f"✅ ColBERT embedding: {len(colbert_result)} tokens")
        logger.info("💡 Sparse embeddings (BM25) are pre-processed and stored in Qdrant")
        
        # Calculate cache size
        cache_size = sum(f.stat().st_size for f in cache_path.rglob("*") if f.is_file())
        cache_size_mb = cache_size / (1024 * 1024)
        
        logger.info(f"📊 Cache size: {cache_size_mb:.1f} MB")
        logger.info("🚀 Models are ready for production use!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Download failed: {e}")
        return False

def check_models(cache_dir: str = "./models"):
    """Check if models are already downloaded."""
    cache_path = Path(cache_dir)
    
    if not cache_path.exists():
        logger.info("📁 Cache directory doesn't exist")
        return False
    
    # Check for model files (simplified check)
    has_models = False
    for model_file in cache_path.rglob("*.bin"):
        has_models = True
        break
    
    if not has_models:
        for model_file in cache_path.rglob("*.safetensors"):
            has_models = True
            break
    
    if has_models:
        cache_size = sum(f.stat().st_size for f in cache_path.rglob("*") if f.is_file())
        cache_size_mb = cache_size / (1024 * 1024)
        file_count = len(list(cache_path.rglob("*")))
        
        logger.info(f"✅ Models found in cache")
        logger.info(f"📊 Cache size: {cache_size_mb:.1f} MB")
        logger.info(f"📊 Files: {file_count}")
        return True
    else:
        logger.info("❌ No models found in cache")
        return False

def main():
    """Main function."""
    cache_dir = os.environ.get("MODEL_CACHE_DIR", "./models")
    
    print("🤖 Simple Model Download Script")
    print("=" * 40)
    print(f"📁 Cache directory: {cache_dir}")
    print(f"🔧 Models to download:")
    for model_type, model_name in MODELS.items():
        print(f"   {model_type}: {model_name}")
    print("")
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check if models already exist
    if check_models(cache_dir):
        response = input("🤔 Models already exist. Re-download? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            logger.info("✅ Using existing models")
            return
    
    # Confirm download
    response = input("🤔 Download models? This will use ~2GB disk space. (y/N): ")
    if response.lower() not in ['y', 'yes']:
        logger.info("❌ Download cancelled")
        return
    
    # Download models
    success = download_models(cache_dir)
    
    if success:
        logger.info("🎉 Download completed successfully!")
        logger.info("💡 You can now start the server with PRELOAD_MODELS=true")
    else:
        logger.error("❌ Download failed")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help"]:
        print("""
🤖 Simple Model Download Script

Downloads embedding models for the BC Activity Assistant.

Usage:
    python download_models_simple.py

Environment Variables:
    MODEL_CACHE_DIR    Directory to store models (default: ./models)

Models:
    • BAAI/bge-base-en-v1.5 (Dense embeddings)
    • Qdrant/bm25 (Sparse embeddings)
    • colbert-ir/colbertv2.0 (ColBERT reranking)

Total size: ~2GB
""")
        sys.exit(0)
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Download interrupted")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)
