#!/usr/bin/env python3
"""
Test script for Kimi K2 model integration with ADK.

This script tests the Novita AI Kimi K2 model integration through LiteLLM
to ensure proper configuration and functionality.
"""

import asyncio
import logging
import sys
import os

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from google.adk.agents import Agent
from google.adk.models.lite_llm import Lite<PERSON>lm
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types
from multi_tool_agent.config import AgentConfig

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test agent using Novita AI Kimi K2
kimi_test_agent = Agent(
    name="KimiTestAgent",
    model=LiteLlm(model=AgentConfig.KIMI_MODEL),
    description="Test agent using the Kimi K2 model for verification.",
    instruction=(
        "You are a test assistant powered by the Kimi K2 model. "
        "Demonstrate your capabilities by providing detailed, well-structured answers. "
        "Show your advanced reasoning and long-context understanding abilities."
    ),
)

async def test_kimi_basic_functionality():
    """Test basic functionality of the Kimi K2 model."""
    logger.info("🧪 Testing Kimi K2 basic functionality...")
    
    # Check if the API key is set
    if not AgentConfig.NOVITA_API_KEY:
        logger.error("❌ NOVITA_API_KEY is not set in environment variables")
        logger.info("Please set NOVITA_API_KEY in your .env file before running this test")
        return False
    
    try:
        # Session Management
        session_service = InMemorySessionService()
        APP_NAME, USER_ID, SESSION_ID = "kimi_test_app", "test_user_01", "session_01"
        await session_service.create_session(
            app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
        )
        
        # Runner
        runner = Runner(agent=kimi_test_agent, app_name=APP_NAME, session_service=session_service)
        logger.info(f"✅ Runner created for agent '{runner.agent.name}'")
        
        # Test Query
        query = "Explain the benefits of swimming lessons for children in three detailed paragraphs, including physical, mental, and social aspects."
        logger.info(f"📝 Test Query: {query}")
        
        content = types.Content(role='user', parts=[types.Part(text=query)])
        
        final_response_text = "Agent did not produce a final response."
        
        async for event in runner.run_async(
            user_id=USER_ID,
            session_id=SESSION_ID,
            new_message=content
        ):
            if event.is_final_response() and event.content and event.content.parts:
                final_response_text = event.content.parts[0].text.strip()
                break
        
        logger.info(f"🤖 Kimi Response:\n{final_response_text}")
        
        # Basic validation
        if len(final_response_text) > 100 and "swimming" in final_response_text.lower():
            logger.info("✅ Kimi K2 integration test PASSED")
            return True
        else:
            logger.error("❌ Kimi K2 integration test FAILED - Response too short or irrelevant")
            return False
            
    except Exception as e:
        logger.error(f"❌ Kimi K2 integration test FAILED with error: {str(e)}")
        return False

async def test_kimi_complex_reasoning():
    """Test Kimi K2's complex reasoning capabilities."""
    logger.info("🧠 Testing Kimi K2 complex reasoning...")
    
    try:
        # Session Management
        session_service = InMemorySessionService()
        APP_NAME, USER_ID, SESSION_ID = "kimi_complex_test", "test_user_02", "session_02"
        await session_service.create_session(
            app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
        )
        
        # Runner
        runner = Runner(agent=kimi_test_agent, app_name=APP_NAME, session_service=session_service)
        
        # Complex reasoning query
        complex_query = """
        I have three children: a 4-year-old who is afraid of water, a 7-year-old who loves swimming but has scheduling conflicts with soccer, 
        and a 10-year-old who wants to advance to competitive swimming. Analyze this situation and provide a comprehensive plan for 
        each child's swimming development, considering their individual needs, potential scheduling solutions, and progression pathways. 
        Include specific recommendations for class types, timing strategies, and how to address the 4-year-old's fear of water.
        """
        
        logger.info(f"🧩 Complex Query: {complex_query[:100]}...")
        
        content = types.Content(role='user', parts=[types.Part(text=complex_query)])
        
        final_response_text = "Agent did not produce a final response."
        
        async for event in runner.run_async(
            user_id=USER_ID,
            session_id=SESSION_ID,
            new_message=content
        ):
            if event.is_final_response() and event.content and event.content.parts:
                final_response_text = event.content.parts[0].text.strip()
                break
        
        logger.info(f"🧠 Kimi Complex Response:\n{final_response_text}")
        
        # Validation for complex reasoning
        keywords = ["4-year-old", "7-year-old", "10-year-old", "plan", "recommendation"]
        found_keywords = sum(1 for keyword in keywords if keyword in final_response_text.lower())
        
        if len(final_response_text) > 500 and found_keywords >= 3:
            logger.info("✅ Kimi K2 complex reasoning test PASSED")
            return True
        else:
            logger.error("❌ Kimi K2 complex reasoning test FAILED - Response lacks depth or specificity")
            return False
            
    except Exception as e:
        logger.error(f"❌ Kimi K2 complex reasoning test FAILED with error: {str(e)}")
        return False

async def main():
    """Run all Kimi K2 integration tests."""
    logger.info("🚀 Starting Kimi K2 Integration Tests")
    logger.info("=" * 60)
    
    # Test 1: Basic functionality
    basic_test_passed = await test_kimi_basic_functionality()
    
    logger.info("-" * 60)
    
    # Test 2: Complex reasoning
    complex_test_passed = await test_kimi_complex_reasoning()
    
    logger.info("=" * 60)
    
    # Summary
    if basic_test_passed and complex_test_passed:
        logger.info("🎉 ALL KIMI K2 INTEGRATION TESTS PASSED!")
        logger.info("✅ Kimi K2 model is properly integrated and functional")
    else:
        logger.error("❌ SOME KIMI K2 INTEGRATION TESTS FAILED")
        logger.info("Please check the configuration and API key setup")
    
    return basic_test_passed and complex_test_passed

if __name__ == "__main__":
    # Enable LiteLLM debugging for troubleshooting
    import litellm
    litellm.set_verbose = True
    
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
