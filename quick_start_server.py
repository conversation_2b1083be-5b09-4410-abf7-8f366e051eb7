#!/usr/bin/env python3
"""
Quick Start Server - Minimal version to get WebSocket working
This bypasses complex rate limiting for now and focuses on connectivity.
"""

import os
import json
import asyncio
import logging
from datetime import datetime
from typing import AsyncGenerator, Dict, Any

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="Multi-Tool Agent Quick Start")

# CORS middleware for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "agent": "BC Activity Assistant (Quick Start)",
        "features": [
            "WebSocket Support",
            "Basic Agent Functionality", 
            "Rate Limiting Ready",
            "Kimi K2 Integration"
        ],
        "timestamp": datetime.now().isoformat()
    }

# Simple agent response simulator
async def simulate_agent_response(query: str) -> AsyncGenerator[Dict[str, Any], None]:
    """Simulate agent responses for testing WebSocket connectivity."""
    
    # Connection event
    yield {
        "type": "connection",
        "data": {"status": "connected", "agent": "BC Activity Assistant"}
    }
    
    # Processing event
    yield {
        "type": "text_chunk", 
        "data": {"text": f"🔍 Processing your query: '{query}'\n\n"}
    }
    
    await asyncio.sleep(0.5)
    
    # Tool call start
    yield {
        "type": "tool_call_start",
        "data": {"tool": "activity_search", "query": query}
    }
    
    await asyncio.sleep(1.0)
    
    # Simulated search results
    yield {
        "type": "text_chunk",
        "data": {"text": "✅ Found relevant activities in our database!\n\n"}
    }
    
    # Activity card
    yield {
        "type": "activity_card",
        "data": {
            "name": "Sample Swimming Class",
            "category": "Aquatics",
            "location": "New Westminster",
            "age_info": "5-12 years",
            "price": "$45.00",
            "schedule": "Mondays 4:00-5:00 PM"
        }
    }
    
    await asyncio.sleep(0.5)
    
    # Tool call complete
    yield {
        "type": "tool_call_complete",
        "data": {"tool": "activity_search", "status": "success"}
    }
    
    # Final response
    yield {
        "type": "text_chunk",
        "data": {"text": "\n🎯 **Summary**: I found swimming classes that match your criteria. The classes are available on Mondays and are perfect for children aged 5-12 years.\n\n"}
    }
    
    yield {
        "type": "text_chunk",
        "data": {"text": "💡 **Next Steps**: Would you like me to help you register for this class or find more options?\n\n"}
    }
    
    # Memory update
    yield {
        "type": "memory_update",
        "data": {"action": "saved", "content": f"User searched for: {query}"}
    }
    
    # Turn complete
    yield {
        "type": "turn_complete",
        "data": {"status": "success", "timestamp": datetime.now().isoformat()}
    }

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication."""
    await websocket.accept()
    logger.info("✅ WebSocket connected")
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            logger.info(f"📨 Received message: {message.get('type', 'unknown')}")
            
            if message.get("type") == "user_message":
                user_query = message.get("content", "")
                logger.info(f"🔍 Processing query: {user_query}")
                
                # Stream agent responses
                async for event in simulate_agent_response(user_query):
                    await websocket.send_text(json.dumps(event))
                    
            elif message.get("type") == "ping":
                # Respond to ping
                await websocket.send_text(json.dumps({
                    "type": "pong",
                    "data": {"timestamp": datetime.now().isoformat()}
                }))
                
    except WebSocketDisconnect:
        logger.info("🔌 WebSocket disconnected")
    except Exception as e:
        logger.error(f"❌ WebSocket error: {e}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "data": {"message": str(e)}
        }))

# API stats endpoint (simplified)
@app.get("/api-stats")
async def api_stats():
    """Get API usage statistics (simplified version)."""
    return {
        "status": "quick_start_mode",
        "message": "Rate limiting and monitoring will be available in full version",
        "kimi_usage": "Strategic usage for final output, delegation, and tool calling",
        "gemini_usage": "Unlimited usage for supporting tasks",
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 Starting Quick Start Server...")
    print("📡 Frontend should connect to: http://localhost:8080")
    print("🔧 Health check: http://localhost:8080/health")
    print("📊 API stats: http://localhost:8080/api-stats")
    print("")
    print("🧪 This is a simplified version for testing WebSocket connectivity.")
    print("🎯 Once connectivity is confirmed, we'll enable full rate limiting.")
    print("=" * 60)
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8080,
        log_level="info"
    )
