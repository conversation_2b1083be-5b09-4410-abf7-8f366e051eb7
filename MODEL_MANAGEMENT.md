# Model Management for Production

This document explains how to manage embedding models for production deployment.

## Overview

The agent uses two embedding models for advanced search:
- **Dense embeddings**: `BAAI/bge-base-en-v1.5` (~500MB) - for semantic search
- **ColBERT reranking**: `colbert-ir/colbertv2.0` (~1GB) - for precision reranking

**Note**: Sparse embeddings (BM25) are **pre-processed and stored in Qdrant**, not generated in real-time.

**Total size**: ~1.5GB

## Quick Start

### 1. Download Models (One-time setup)

```bash
# Download all models to local cache
python download_models.py
```

### 2. Start Server with Models

```bash
# Start with model preloading (advanced search enabled)
PRELOAD_MODELS=true python start_with_models.py

# OR start with simple search (faster startup)
python start_with_models.py
```

## Model Management Commands

### Download Models
```bash
python download_models.py
```

### Check Model Status
```bash
python start_with_models.py --check
```

### Start with Different Configurations
```bash
# Fast startup (simple search)
python adk_server.py

# With model preloading (advanced search)
PRELOAD_MODELS=true python start_with_models.py

# Custom cache directory
MODEL_CACHE_DIR=/path/to/models python start_with_models.py
```

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `MODEL_CACHE_DIR` | `./models` | Directory to store cached models |
| `PRELOAD_MODELS` | `false` | Load models on startup |

## Search Modes

### Simple Search (Default)
- ✅ **Fast startup** (no model loading)
- ✅ **Works without models** downloaded
- ✅ **Basic text matching** via Qdrant
- ⚠️ **Limited semantic understanding**

### Advanced Search (With Models)
- 🚀 **3-stage reranking** for best results
- 🚀 **Semantic understanding** with dense embeddings
- 🚀 **Keyword matching** with sparse embeddings
- 🚀 **Precision reranking** with ColBERT
- ⏳ **Requires model download** (~2GB)
- ⏳ **Slower startup** (model loading)

## Production Deployment

### Option 1: Pre-download Models in Container

```dockerfile
# In your Dockerfile
COPY requirements.txt .
RUN pip install -r requirements.txt

# Download models during build
COPY download_models.py .
COPY multi_tool_agent/ ./multi_tool_agent/
RUN python download_models.py

# Set environment for production
ENV PRELOAD_MODELS=true
ENV MODEL_CACHE_DIR=/app/models
```

### Option 2: Download on First Run

```bash
# Let models download on first startup
docker run -e PRELOAD_MODELS=true your-agent-image
```

### Option 3: Shared Model Volume

```bash
# Download models to shared volume
docker run -v models:/app/models your-agent-image python download_models.py

# Use shared models in production
docker run -v models:/app/models -e PRELOAD_MODELS=true your-agent-image
```

## Model Cache Structure

```
models/
├── dense/
│   └── BAAI_bge-base-en-v1.5/
├── sparse/
│   └── Qdrant_bm25/
└── colbert/
    └── colbert-ir_colbertv2.0/
```

## Troubleshooting

### Models Not Loading
```bash
# Check model status
python start_with_models.py --check

# Re-download models
rm -rf models/
python download_models.py
```

### Slow Startup
```bash
# Use simple search for faster startup
python adk_server.py

# OR download models first
python download_models.py
PRELOAD_MODELS=true python start_with_models.py
```

### Disk Space Issues
```bash
# Check cache size
python start_with_models.py --check

# Clear cache if needed
rm -rf models/
```

## Performance Comparison

| Mode | Startup Time | Search Quality | Memory Usage |
|------|-------------|----------------|--------------|
| Simple | ~5 seconds | Good | ~200MB |
| Advanced | ~30 seconds | Excellent | ~2GB |

## Best Practices

1. **Development**: Use simple search for fast iteration
2. **Production**: Pre-download models in container build
3. **CI/CD**: Cache model directory between builds
4. **Monitoring**: Check model loading status in logs
5. **Scaling**: Share model cache across instances

## API Endpoints

Check model status via API:
```bash
curl http://localhost:8080/api-stats
```

Response includes model information:
```json
{
  "model_cache": {
    "cache_dir": "./models",
    "cache_size_mb": 1847.3,
    "models_loaded": true,
    "model_status": {
      "dense": true,
      "sparse": true, 
      "colbert": true
    }
  }
}
```
