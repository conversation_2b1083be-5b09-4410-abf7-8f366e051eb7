import os
from dotenv import load_dotenv

# Load environment variables from a .env file in the project's root directory
load_dotenv()

class AgentConfig:
    """Centralized configuration for the multi-tool agent."""

    # --- API Keys ---
    GOOGLE_API_KEY = os.environ.get("GOOGLE_API_KEY")
    NOVITA_API_KEY = os.environ.get("NOVITA_API_KEY")

    # --- Qdrant Vector DB Configuration ---
    QDRANT_URL = os.environ.get("QDRANT_URL", "http://localhost:6333")
    QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY") 
    QDRANT_COLLECTION_NAME = "community_activities_v2" # Updated for new schema

    # --- Neo4j and Graphiti (if used) ---
    NEO4J_URI = os.environ.get("NEO4J_URI", "neo4j://localhost:7687")
    NEO4J_USER = os.environ.get("NEO4J_USER", "neo4j")
    NEO4J_PASSWORD = os.environ.get("NEO4J_PASSWORD", "password")
    GRAPHITI_MODEL = os.environ.get("GRAPHITI_MODEL", "gemini-1.5-flash")

    # --- Model Configuration ---
    # Kimi K2 Model Configuration - Using Kimi for all tasks
    KIMI_MODEL = "novita/moonshotai/kimi-k2-instruct"

    # Model for the main orchestrator agent (controls the logic and planning)
    ORCHESTRATOR_MODEL = KIMI_MODEL # Use Kimi K2 for advanced reasoning

    # Model for other specialist agents - all using Kimi K2
    SEARCH_AGENT_MODEL = KIMI_MODEL
    RESPONSE_AGENT_MODEL = KIMI_MODEL

    # --- Embedding Model Configuration for 3-Stage Hybrid Search ---
    # Stage 1: Dense vector model for semantic meaning
    EMBEDDING_MODEL = "BAAI/bge-base-en-v1.5"
    EMBEDDING_DIM = 768

    # Stage 1: Sparse vector model for keyword matching
    SPARSE_EMBEDDING_MODEL = "Qdrant/bm25"

    # Stage 2: Late-interaction model for reranking
    COLBERT_MODEL = "colbert-ir/colbertv2.0"
    COLBERT_DIM = 128 # The dimension for ColBERT vectors

    # --- Advanced RAG Configuration ---
    # Binary quantization for ultra-fast retrieval (10-50x speed improvement)
    ENABLE_BINARY_QUANTIZATION = True

    # 3-Stage reranking funnel parameters
    STAGE1_CANDIDATE_MULTIPLIER = 6  # Reduced from 8 for faster Stage 1
    MIN_STAGE1_CANDIDATES = 30       # Reduced from 50 for faster processing
    RERANK_SCORE_THRESHOLD = 0.05    # Lowered threshold for more results

    # Performance optimization
    ENABLE_RESCORE = True            # Use rescoring with quantization for precision
    MAX_CONCURRENT_SEARCHES = 5      # Limit concurrent search operations

    # --- API Rate Limiting Configuration ---
    # Novita AI Kimi K2 has 10 RPM limit - optimize for minimal API calls
    ENABLE_API_OPTIMIZATION = True   # Enable aggressive API call reduction
    MAX_API_CALLS_PER_REQUEST = 1    # Maximum 1 API call per user request
    ENABLE_RESPONSE_CACHING = True   # Cache LLM responses
    CACHE_TTL_MINUTES = 30           # Cache responses for 30 minutes
    ULTRA_LOW_API_MODE = True        # Use tools without LLM when possible

    # Ultra-fast mode settings (for production)
    ULTRA_FAST_MODE = True           # Enable aggressive speed optimizations
    FAST_MODE_CANDIDATE_LIMIT = 20   # Even fewer candidates in ultra-fast mode
    CACHE_TTL_SECONDS = 600          # 10-minute cache for frequent queries
