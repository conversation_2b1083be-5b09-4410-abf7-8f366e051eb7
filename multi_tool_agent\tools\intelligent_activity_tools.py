"""
Intelligent Activity Tools - Token-Efficient Architecture
Uses smart tools to offload heavy processing from LLM to save tokens and costs.
"""

import logging
import time
import hashlib
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from google.adk.tools import ToolContext

logger = logging.getLogger(__name__)

# Import existing search infrastructure
from .activity_search_tools import _perform_advanced_search, _simple_search_fallback, _create_qdrant_filter, ActivityFilters

# Simple response cache for API optimization (10 RPM limit)
_response_cache = {}
_cache_ttl = 1800  # 30 minutes

def _get_cache_key(query: str, filters: Dict[str, Any], extra: str = "") -> str:
    """Generate cache key for responses."""
    cache_input = f"{query.lower().strip()}|{str(sorted(filters.items()))}|{extra}"
    return hashlib.md5(cache_input.encode()).hexdigest()

def _get_cached_response(cache_key: str) -> Optional[Any]:
    """Get cached response if available and not expired."""
    if cache_key in _response_cache:
        response, timestamp = _response_cache[cache_key]
        if time.time() - timestamp < _cache_ttl:
            logger.info(f"🎯 Cache HIT for key: {cache_key[:8]}...")
            return response
        else:
            del _response_cache[cache_key]
    return None

def _cache_response(cache_key: str, response: Any) -> None:
    """Cache a response."""
    _response_cache[cache_key] = (response, time.time())
    logger.info(f"💾 Cached response for key: {cache_key[:8]}...")

class ActivityAnalyzer:
    """Smart analyzer that processes activity data without LLM involvement."""
    
    @staticmethod
    def find_patterns(activities: List[Dict], pattern_type: str) -> Dict[str, Any]:
        """
        Analyzes activities for specific patterns without sending data to LLM.
        Returns structured insights that LLM can easily interpret.
        """
        if pattern_type == "back_to_back":
            return ActivityAnalyzer._analyze_back_to_back(activities)
        elif pattern_type == "schedule_conflicts":
            return ActivityAnalyzer._analyze_conflicts(activities)
        elif pattern_type == "progression_paths":
            return ActivityAnalyzer._analyze_progressions(activities)
        elif pattern_type == "cost_optimization":
            return ActivityAnalyzer._analyze_costs(activities)
        else:
            return {"error": f"Unknown pattern type: {pattern_type}"}
    
    @staticmethod
    def _analyze_back_to_back(activities: List[Dict]) -> Dict[str, Any]:
        """Find consecutive classes with minimal token usage."""
        facility_groups = {}
        
        # Group by facility and date
        for activity in activities:
            facility = activity.get("facility", "Unknown")
            date = activity.get("start_date")
            if not date:
                continue
                
            key = f"{facility}_{date}"
            if key not in facility_groups:
                facility_groups[key] = []
            facility_groups[key].append(activity)
        
        opportunities = []
        
        for group_key, group_activities in facility_groups.items():
            facility, date = group_key.split("_", 1)
            
            # Sort by start time
            timed_activities = []
            for activity in group_activities:
                start_time_str = activity.get("start_time_iso")
                if start_time_str:
                    try:
                        start_time = datetime.strptime(start_time_str, "%H:%M:%S").time()
                        timed_activities.append((start_time, activity))
                    except ValueError:
                        continue
            
            timed_activities.sort(key=lambda x: x[0])
            
            # Find consecutive pairs
            for i in range(len(timed_activities) - 1):
                current_time, current_activity = timed_activities[i]
                next_time, next_activity = timed_activities[i + 1]
                
                current_end_str = current_activity.get("end_time_iso")
                if current_end_str:
                    try:
                        current_end = datetime.strptime(current_end_str, "%H:%M:%S").time()
                        
                        # Calculate gap
                        base_date = datetime.today()
                        current_end_dt = datetime.combine(base_date, current_end)
                        next_start_dt = datetime.combine(base_date, next_time)
                        gap_minutes = (next_start_dt - current_end_dt).total_seconds() / 60
                        
                        if 0 <= gap_minutes <= 30:  # 0-30 minute gap
                            opportunities.append({
                                "facility": facility,
                                "date": date,
                                "gap_minutes": int(gap_minutes),
                                "first_class": {
                                    "name": current_activity.get("name"),
                                    "time": f"{current_activity.get('start_time_iso')} - {current_activity.get('end_time_iso')}",
                                    "price": current_activity.get("price_numeric", 0)
                                },
                                "second_class": {
                                    "name": next_activity.get("name"),
                                    "time": f"{next_activity.get('start_time_iso')} - {next_activity.get('end_time_iso')}",
                                    "price": next_activity.get("price_numeric", 0)
                                },
                                "total_price": (current_activity.get("price_numeric", 0) + next_activity.get("price_numeric", 0)),
                                "total_duration": f"{current_activity.get('start_time_iso')} - {next_activity.get('end_time_iso')}"
                            })
                    except ValueError:
                        continue
        
        return {
            "pattern_type": "back_to_back",
            "total_opportunities": len(opportunities),
            "opportunities": opportunities[:8],  # Limit for token efficiency
            "summary": f"Found {len(opportunities)} back-to-back opportunities across {len(facility_groups)} facility-date combinations"
        }
    
    @staticmethod
    def _analyze_conflicts(activities: List[Dict]) -> Dict[str, Any]:
        """Analyze schedule conflicts."""
        conflicts = []
        # Implementation for conflict detection
        return {
            "pattern_type": "schedule_conflicts",
            "conflicts": conflicts,
            "summary": f"Found {len(conflicts)} potential schedule conflicts"
        }
    
    @staticmethod
    def _analyze_progressions(activities: List[Dict]) -> Dict[str, Any]:
        """Analyze skill progression paths."""
        progressions = {}
        # Group by activity type and analyze levels
        for activity in activities:
            name = activity.get("name", "")
            if "level" in name.lower():
                # Extract progression info
                pass
        
        return {
            "pattern_type": "progression_paths",
            "progressions": progressions,
            "summary": "Skill progression analysis"
        }
    
    @staticmethod
    def _analyze_costs(activities: List[Dict]) -> Dict[str, Any]:
        """Analyze cost optimization opportunities."""
        cost_analysis = {
            "cheapest_options": sorted(activities, key=lambda x: x.get("price_numeric", float('inf')))[:5],
            "price_ranges": {},
            "bulk_discounts": []
        }
        
        return {
            "pattern_type": "cost_optimization",
            "analysis": cost_analysis,
            "summary": "Cost optimization analysis"
        }

# Smart Tools that use ActivityAnalyzer

async def discover_activity_patterns(
    query: str,
    filters: Dict[str, Any],
    pattern_types: List[str],
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Intelligent pattern discovery tool that processes data efficiently.
    
    Args:
        query: Search query for activities
        filters: Activity filters (age, location, etc.)
        pattern_types: List of patterns to discover (back_to_back, conflicts, progressions, costs)
    
    Returns:
        Structured insights without heavy token usage
    """
    try:
        # Validate filters
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        logger.info(f"🔍 Discovering patterns: {pattern_types} for query: '{query}'")
        
        # Get activities (limit to reasonable number) - try advanced search first
        qdrant_filter = _create_qdrant_filter(filters_dict)
        try:
            activities = await _perform_advanced_search(query, qdrant_filter, limit=50)
        except Exception as e:
            logger.warning(f"⚠️ Advanced search failed, using fallback: {e}")
            activities = await _simple_search_fallback(query, qdrant_filter, limit=50)
        
        if not activities:
            return {
                "status": "success",
                "message": "No activities found matching your criteria",
                "patterns": {}
            }
        
        # Analyze patterns efficiently (no LLM involved)
        patterns = {}
        for pattern_type in pattern_types:
            patterns[pattern_type] = ActivityAnalyzer.find_patterns(activities, pattern_type)
        
        # Return concise, structured results
        return {
            "status": "success",
            "total_activities_analyzed": len(activities),
            "patterns": patterns,
            "query_info": {
                "search_query": query,
                "filters_applied": filters_dict,
                "patterns_requested": pattern_types
            }
        }
        
    except Exception as e:
        logger.error(f"Error in discover_activity_patterns: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred during pattern discovery"}

async def get_activity_summary(
    query: str,
    filters: Dict[str, Any],
    summary_type: str,
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Get concise activity summary without sending full data to LLM.
    
    Args:
        query: Search query
        filters: Activity filters
        summary_type: Type of summary (quick, detailed, comparison)
    
    Returns:
        Structured summary ready for LLM interpretation
    """
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        logger.info(f"📊 Generating {summary_type} summary for: '{query}'")
        
        # Get activities - try advanced search first
        qdrant_filter = _create_qdrant_filter(filters_dict)
        limit = 20 if summary_type == "quick" else 40
        try:
            activities = await _perform_advanced_search(query, qdrant_filter, limit=limit)
        except Exception as e:
            logger.warning(f"⚠️ Advanced search failed, using fallback: {e}")
            activities = await _simple_search_fallback(query, qdrant_filter, limit=limit)
        
        if not activities:
            return {
                "status": "success",
                "summary_type": summary_type,
                "message": "No activities found",
                "stats": {"total_found": 0}
            }
        
        # Generate summary statistics (no LLM needed)
        facilities = set(a.get("facility") for a in activities if a.get("facility"))
        categories = set(a.get("category") for a in activities if a.get("category"))
        price_range = {
            "min": min((a.get("price_numeric", 0) for a in activities if a.get("price_numeric")), default=0),
            "max": max((a.get("price_numeric", 0) for a in activities if a.get("price_numeric")), default=0),
            "average": sum(a.get("price_numeric", 0) for a in activities) / len(activities) if activities else 0
        }
        
        # Sample activities for LLM (much smaller payload)
        sample_activities = activities[:5] if summary_type == "quick" else activities[:10]
        
        return {
            "status": "success",
            "summary_type": summary_type,
            "stats": {
                "total_found": len(activities),
                "facilities_count": len(facilities),
                "categories_count": len(categories),
                "price_range": price_range
            },
            "facilities": list(facilities),
            "categories": list(categories),
            "sample_activities": [
                {
                    "name": a.get("name"),
                    "facility": a.get("facility"),
                    "time": f"{a.get('start_time_iso')} - {a.get('end_time_iso')}",
                    "date": a.get("start_date"),
                    "price": a.get("price_numeric"),
                    "age_range": a.get("display_age")
                }
                for a in sample_activities
            ]
        }
        
    except Exception as e:
        logger.error(f"Error in get_activity_summary: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred generating summary"}

async def check_activity_availability(
    activity_names: List[str],
    preferred_times: List[str],
    filters: Dict[str, Any],
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Check availability for specific activities at preferred times.
    Efficient tool that returns yes/no answers without heavy data.
    """
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        availability_results = {}
        
        for activity_name in activity_names:
            # Search for specific activity - try advanced search first
            qdrant_filter = _create_qdrant_filter(filters_dict)
            try:
                activities = await _perform_advanced_search(activity_name, qdrant_filter, limit=20)
            except Exception as e:
                logger.warning(f"⚠️ Advanced search failed, using fallback: {e}")
                activities = await _simple_search_fallback(activity_name, qdrant_filter, limit=20)
            
            # Check time preferences
            available_times = []
            for activity in activities:
                start_time = activity.get("start_time_iso")
                if start_time:
                    available_times.append({
                        "time": start_time,
                        "date": activity.get("start_date"),
                        "facility": activity.get("facility"),
                        "is_open": activity.get("is_open", False)
                    })
            
            # Match with preferred times
            matches = []
            for pref_time in preferred_times:
                for avail in available_times:
                    if pref_time in avail["time"] and avail["is_open"]:
                        matches.append(avail)
            
            availability_results[activity_name] = {
                "total_found": len(activities),
                "available_at_preferred_times": len(matches),
                "matches": matches[:3],  # Limit results
                "has_availability": len(matches) > 0
            }
        
        return {
            "status": "success",
            "availability": availability_results,
            "summary": f"Checked availability for {len(activity_names)} activities"
        }
        
    except Exception as e:
        logger.error(f"Error in check_activity_availability: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred checking availability"}
